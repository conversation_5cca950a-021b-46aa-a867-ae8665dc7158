<template>
  <div class="jsgjzl-tjb-container">
    <div class="query-header">
      <div class="search-box">
        <a-range-picker
          v-model:value="dateRange"
          :placeholder="['开始日期', '结束日期']"
        />
        <a-button type="primary" @click="fetchData" class="refresh-btn">
          查询</a-button
        >
        <!-- <a-button type="primary" class="refresh-btn"> 导出</a-button> -->
        <a-button type="text" @click="refresh" class="refresh-btn">
          <a-icon type="reload" />
          刷新
        </a-button>
      </div>
    </div>

    <div class="main">
      <div class="panel">
        <div class="title">
          大庆油田龙丰实业有限公司天然气系统生产运行情况统计报表
        </div>
        <!-- <div class="header-info">
                    <div class="info-item">
                        <span class="label">填报企业：</span>
                        <span class="value">{{ companyName }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">填报人：</span>
                        <span class="value">{{ reporter }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">联系电话：</span>
                        <span class="value">{{ contactPhone }}</span>
                    </div>
                </div> -->

        <a-table
          :columns="columns"
          :data-source="tableData"
          :pagination="false"
          :bordered="true"
          :scroll="{ x: 1800 }"
          size="middle"
        >
        </a-table>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { Table, Button, Icon, DatePicker } from '@h3/antd-vue';
import Api from '../../ly-api';
import moment from 'moment';
@Component({
  name: 'JsgjzlTjb',
  components: {
    ATable: Table,
    AButton: Button,
    AIcon: Icon,
    ARangePicker: DatePicker.RangePicker,
  },
})
export default class JsgjzlTjb extends Vue {
  year: string = '';
  month: string = '';
  companyName: string = '';
  reporter: string = '';
  contactPhone: string = '';
  reception: any = []; //接收情况统计
  ismonth: any = moment(); // 月份
  // 表格数据（不包含合计行）
  dataSource = [];
  dateRange: any = [moment(), moment()];

  // 初始化空数据
  initEmptyData() {
    return {
      fgs: 0,
      xddw: 0,
      xmmc: 0,
      sgdd: 0,
      sgnr: 0,
      sgsjd: 0,
      fzr: 0,
      fzrdh: 0,
      sfycbs: 0,
      cbsdw: 0,
      cbsfzr: 0,
      cbsfzrdh: 0,
      xzsgsfysxt: 0,
      bz: 0,
      sgzq: 0,
      sjwcgzl: 0,
      ljwcgzl: 0,
      ljcz: 0,
    };
  }

  // 计算表格数据（包含合计行）
  get tableData() {
    // const totalRow = {
    //     key: 'total',
    //     name: '合计',
    //     jsYear_wzj: this.sumColumn('jsYear_wzj'),
    //     jsYear_bhg: this.sumColumn('jsYear_bhg'),
    //     jsYear_hgl: this.sumColumn('jsYear_hgl'),
    //     jsMonth_wzj: this.sumColumn('jsMonth_wzj'),
    //     jsMonth_bhg: this.sumColumn('jsMonth_bhg'),
    //     jsMonth_hgl: this.sumColumn('jsMonth_hgl'),
    //     gjYear_wzj: this.sumColumn('gjYear_wzj'),
    //     gjYear_bhg: this.sumColumn('gjYear_bhg'),
    //     gjYear_hgl: this.sumColumn('gjYear_hgl'),
    //     gjMonth_wzj: this.sumColumn('gjMonth_wzj'),
    //     gjMonth_bhg: this.sumColumn('gjMonth_bhg'),
    //     gjMonth_hgl: this.sumColumn('gjMonth_hgl'),
    // };
    // 返回包含合计行的数据

    const data = [...this.dataSource];
    // 添加合计行
    const totalRow = {
      key: 'total',
      fgs: '合计',
      ljcz: this.sumColumn('ljcz'),
    };
    return [...data, totalRow];

    // return [...this.dataSource];
  }

  // 求和指定列
  sumColumn(columnKey: string) {
    return this.dataSource.reduce((sum, row) => sum + (row[columnKey] || 0), 0);
  }

  // 表格列定义
  columns = [
    {
      title: '分公司',
      dataIndex: 'fgs',
      key: 'fgs',
      // fixed: 'left',
      width: 150,
      customRender: (text: any, row: any, index: number) => {
        const obj: {
          children: any;
          attrs: {
            rowSpan?: number;
          };
        } = {
          children: text,
          attrs: {},
        };

        if (row.fgsRowSpan) {
          obj.attrs.rowSpan = row.fgsRowSpan;
        } else if (row.fgsHide) {
          obj.attrs.rowSpan = 0;
        }

        return obj;
      },
    },
    {
      title: '小队级',
      dataIndex: 'xddw', // 新增字段映射
      key: 'xddw',
      width: 100,
    },
    {
      title: '项目名称',
      dataIndex: 'xmmc', // 新增字段映射
      key: 'xmmc',
      width: 200,
    },
    {
      title: '具体施工地点',
      dataIndex: 'sgdd', // 新增字段映射
      key: 'sgdd',
      width: 150,
    },
    {
      title: '施工内容',
      dataIndex: 'sgnr', // 新增字段映射
      key: 'sgnr',
      width: 200,
    },
    {
      title: '施工时间段',
      dataIndex: 'sgsjd', // 新增字段映射
      key: 'sgsjd',
      width: 120,
    },
    {
      title: '负责人',
      dataIndex: 'fzr', // 新增字段映射
      key: 'fzr',
      width: 100,
    },
    {
      title: '负责人电话',
      dataIndex: 'fzrdh', // 新增字段映射
      key: 'fzrdh',
      width: 120,
    },
    {
      title: '是否有承包商',
      dataIndex: 'sfycbs', // 新增字段映射
      key: 'sfycbs',
      width: 70,
    },
    {
      title: '承包商单位（全称）',
      dataIndex: 'cbsdw', // 新增字段映射
      key: 'cbsdw',
      width: 200,
    },
    {
      title: '承包商负责人',
      dataIndex: 'cbsfzr', // 新增字段映射
      key: 'cbsfzr',
      width: 70,
    },
    {
      title: '承包商负责人电话',
      dataIndex: 'cbsfzrdh', // 新增字段映射
      key: 'cbsfzrdh',
      width: 150,
    },
    {
      title: '现场施工是否安装摄像头',
      dataIndex: 'xzsgsfysxt', // 新增字段映射
      key: 'xzsgsfysxt',
      width: 120,
    },
    {
      title: '备注',
      dataIndex: 'bz', // 新增字段映射
      key: 'bz',
      width: 200,
    },
    {
      title: '施工周期',
      dataIndex: 'sgzq', // 新增字段映射
      key: 'sgzq',
      width: 100,
    },
    {
      title: '实际完成工作量',
      dataIndex: 'sjwcgzl', // 新增字段映射
      key: 'sjwcgzl',
      width: 300,
    },
    {
      title: '累计完成工作量',
      dataIndex: 'ljwcgzl',
      key: 'ljwcgzl',
      width: 300,
      customRender: (text: any, row: any) => {
        const obj: {
          children: any;
          attrs: {
            rowSpan?: number;
            style?: string;
          };
        } = {
          children: text,
          attrs: {
            style: 'white-space: normal; word-break: break-word;', // 允许自动换行
          },
        };
        if (row.fgsRowSpan) {
          obj.attrs.rowSpan = row.fgsRowSpan;
        } else if (row.fgsHide) {
          obj.attrs.rowSpan = 0;
        }
        return obj;
      },
    },
    {
      title: '累计产值（万元）',
      dataIndex: 'ljcz',
      key: 'ljcz',
      width: 100,
      customRender: (text: any, row: any) => {
        const obj: {
          children: any;
          attrs: {
            rowSpan?: number;
          };
        } = {
          children: text,
          attrs: {},
        };
        if (row.fgsRowSpan) {
          obj.attrs.rowSpan = row.fgsRowSpan;
        } else if (row.fgsHide) {
          obj.attrs.rowSpan = 0;
        }
        return obj;
      },
    },
  ];

  // 生命周期钩子
  mounted() {
    // 这里可以调用API获取数据
    this.fetchData();
  }
  refresh() {
    // this.ismonth = moment();
    // this.companyName = '';
    // this.reporter = '';
    // this.contactPhone = '';
    // this.dateRange = [moment(), moment()];
    this.dataSource = [];
    this.fetchData();
    this.$message.success('刷新成功');
  }

  // 获取数据方法
  async fetchData() {
    // this.companyName = '';
    // this.reporter =  '';
    // this.contactPhone =  '';
    let startDate = this.dateRange[0].format('YYYY-MM-DD');
    let endDate = this.dateRange[1]
      ? this.dateRange[1].format('YYYY-MM-DD')
      : moment().format('YYYY-MM-DD');
    const res = await Api.getList({
      filters: [],
      mobile: false,
      page: 0,
      queryCode: 'scyxtjb',
      schemaCode: 'scyxtjb',
      size: 1000,
      queryVersion: 1,
      queryCondition: [
        [
          [
            {
              propertyCode: 'createdTime',
              propertyType: 3,
              queryFilterType: 'Between',
              propertyValue: `${startDate} 00:00:00;${endDate} 23:59:59`,
            },
          ],
        ],
      ],
      showTotal: true,
    });
    //  this.year = this.ismonth.format('YYYY');
    //  this.month = this.ismonth.format('MM');

    this.dataSource =
      res.data.content?.flatMap((item: any) => {
        // 先找出有fgs值的记录
        let validFgs = '';
        if (item.data?.Sheet1748919873595) {
          const fgsRecord = item.data.Sheet1748919873595.find(
            (r: any) => r.fgs,
          );
          if (fgsRecord) {
            validFgs = fgsRecord.fgs;
          }
        }

        return (
          item.data?.Sheet1748919873595?.map(
            (sheetItem: any, index: number) => ({
              key: `${item.id}_${index}`, // 生成唯一key
              name: sheetItem.yt || 0,
              ...this.initEmptyData(), // 初始化空数据
              fgs: sheetItem.fgs || validFgs, // 使用记录自己的fgs值或有效的fgs值
              xddw: sheetItem.xddw || 0, // 小队级
              xmmc: sheetItem.xmmc || 0, // 项目名称
              sgdd: sheetItem.sgdd || 0, // 具体施工地点
              sgnr: sheetItem.sgnr || 0, // 施工内容
              sgsjd: sheetItem.sgsjd || 0, // 施工时间段
              fzr: sheetItem.fzr || 0, // 负责人
              fzrdh: sheetItem.fzrdh || 0, // 负责人电话
              sfycbs: sheetItem.sfycbs || 0, // 是否有承包商
              cbsdw: sheetItem.cbsdw || 0, // 承包商单位（全称）
              cbsfzr: sheetItem.cbsfzr || 0, // 承包商负责人
              cbsfzrdh: sheetItem.cbsfzrdh || 0, // 承包商负责人电话
              xzsgsfysxt: sheetItem.xzsgsfysxt || 0, // 现场施工是否安装摄像头
              bz: sheetItem.bz || 0, // 备注
              sgzq: sheetItem.sgzq || 0, // 施工周期
              sjwcgzl: sheetItem.sjwcgzl || 0, // 实际完成工作量
              ljwcgzl: sheetItem.ljwcgzl || 0, // 累计完成工作量
              ljcz: sheetItem.ljcz || 0, // 累计产值  （万元）
            }),
          ) || []
        );
      }) || [];
    const mergedData: any[] = [];
    let lastFgs = null;
    let spanCount = 0;
    // 第一次遍历计算每个分公司的行数
    const fgsCountMap = new Map();
    this.dataSource.forEach((item: any) => {
      const count = fgsCountMap.get(item.fgs) || 0;
      fgsCountMap.set(item.fgs, count + 1);
    });

    // 第二次遍历设置合并属性
    this.dataSource.forEach((item: any) => {
      if (item.fgs !== lastFgs) {
        lastFgs = item.fgs;
        spanCount = fgsCountMap.get(item.fgs) || 1;
        mergedData.push({ ...item, fgsRowSpan: spanCount });
      } else {
        mergedData.push({ ...item, fgsHide: true });
      }
    });

    this.dataSource = mergedData;

    this.reception = res.data;
    this.dataSource = [...this.dataSource];
  }
}
</script>

<style lang="less" scoped>
.jsgjzl-tjb-container {
  height: 100%;
  padding: 12px;
  background: #f8f8f8;

  .main {
    height: calc(100vh - 200px); // 固定高度
    background: #fff;
    padding: 12px;
    position: relative;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: auto;

    .panel {
      padding: 24px;
    }

    .title {
      font-size: 18px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 16px;
    }

    .header-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;

      .info-item {
        .label {
          font-weight: bold;
        }
      }
    }
  }

  .query-header {
    margin-bottom: 24px;
    padding: 16px 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.05);

    .page-title {
      color: #1d2129;
      margin-bottom: 16px;
      font-size: 20px;
      font-weight: 600;
    }

    .search-box {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }
}

/deep/ .ant-table {
  .ant-table-tbody > tr:last-child {
    font-weight: bold;
    background-color: #fafafa;

    td {
      border-bottom: 1px solid #f0f0f0;
    }
  }
}
</style>
