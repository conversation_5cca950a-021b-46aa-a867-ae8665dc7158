import templateComponents from 'extension-template/index';
import extendMenuRoutes from 'extension-template/src/views/workflow-center/extend-menu/extend-menu-routes';

export default {
  formDetail: {
    path: '/form/detail',
    name: 'form-detail',
    component: () => import('@/views/form/form-detail.vue'),
  },
  application: {
    // //全局覆盖应用下表单列表数据显示页面
    // applicationList: {
    //   path: 'application-list/:schemaCode',
    //   name: 'applicationList',
    //   component: templateComponents.ApplicationList,
    // },
    // //覆盖指定表单列表数据显示页面
    // specificModelList: {
    //   path: 'specificModelList/:schemaCode',
    //   name: 'specificModelList',
    //   component: templateComponents.SpecificModelList,
    // },
    // 自定义路由测试1
    testSpecificModelList1: {
      path: 'test-specific-model-list1/:schemaCode',
      name: 'testSpecificModelList1',
      component: templateComponents.ApplicationList,
    },
    // 自定义路由测试2
    testSpecificModelList2: {
      path: 'test-specific-model-list2/:schemaCode',
      name: 'testSpecificModelList2',
      component: templateComponents.SpecificModelList,
    },

    /**
     * @description: 测试项目统计表
     * 2024 5-9
     */
    gdzczlxmtjb: {
      path: 'gdzczlxmtjb',
      name: 'gdzczlxmtjb',
      component: () => import('@/../extends/pages/project.vue'),
    },

    gdzczltzhzb: {
      path: 'gdzczltzhzb',
      name: 'gdzczltzhzb',
      component: () => import('@/../extends/pages/summary.vue'),
    },
    zbjhTwo: {
      path: 'zbjh-two',
      name: 'zbjhTwo',
      component: () => import('@/../extends/pages/zbjh/two.vue'),
    },
    ndjhTwo: {
      path: 'ndjh-two',
      name: 'ndjhTwo',
      component: () => import('@/../extends/pages/ndjh/two.vue'),
    },

    // 数据可视化
    datav: {
      path: 'datav',
      name: 'datav',
      component: () => import('@/../extends/pages/datav/datav.vue'),
    },

    // 流动人口统计表
    lfScyxtjb: {
      path: 'lfScyxtjb',
      name: 'lfScyxtjb',
      component: () => import('@/../extends/pages/lf/c-scyxtjb.vue'),
    },
  },
  //流程中心扩展菜单路由示例
  workflowCenter: {
    ...extendMenuRoutes,
  },
} as any;
