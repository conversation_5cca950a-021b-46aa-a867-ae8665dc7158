<template>
  <div class="map-statis">
    <amap ref="map" map-style="amap://styles/62009be025f187dd3eafe327d2e55b8e" class="amap-demo">
    </amap>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
@Component({
  name: 'MapStatis',
})
export default class MapStatis extends Vue {
  mounted() {
    console.log('MapStatis mounted');
    // VueAMap.initAMapApiLoader({
    //   key: '237e965d151c4eddc029f1494cf498c2',
    //   plugin: [
    //     'Geocoder',
    //     'AMap.Geolocation',
    //     'AMap.Autocomplete',
    //     'AMap.PlaceSearch',
    //     'AMap.Scale',
    //     'AMap.OverView',
    //     'AMap.ToolBar',
    //     'AMap.MapType',
    //     'AMap.PolyEditor',
    //     'AMap.CircleEditor',
    //     'AMap.Map',
    //   ],
    //   // 默认高德 sdk 版本为 1.4.4
    //   v: '1.4.14',
    //   uiVersion: '1.0.11',
    // });
  }
}
</script>

<style lang="less" scoped>
.map-statis {
  width: 100%;
  height: 100%;
  .amap-demo {
    width: 100%;
    height: 100%;
  }
}
</style>