<template>
  <div class="datav-nav">
    <div class="datav-nav-left">
      <div class="datav-nav-left-item">
        <div
          class="datav-nav-left-item-title"
          :class="{ 'datav-nav-item-title-active': activeIndex === 0 }"
          @click="handleNavLeftClick(0)"
        >
          生产日报
        </div>
        <div
          class="datav-nav-left-item-title"
          :class="{ 'datav-nav-item-title-active': activeIndex === 1 }"
          @click="handleNavLeftClick(1)"
        >
          每日施工
        </div>
      </div>
    </div>
    <div class="datav-nav-right">
      <div class="datav-nav-right-item">
        <!-- <div
          class="datav-nav-right-item-title"
          :class="{ 'datav-nav-item-title-active': activeIndex === 2 }"
          @click="handleNavRightClick(2)"
        >
          HSE监督管理
        </div>
        <div
          class="datav-nav-right-item-title"
          :class="{ 'datav-nav-item-title-active': activeIndex === 3 }"
          @click="handleNavRightClick(3)"
        >
          专项检查
        </div> -->
        <div
          class="datav-nav-right-item-title"
          :class="{ 'datav-nav-item-title-active': activeIndex === 4 }"
          @click="handleNavRightClick(4)"
        >
          {{ isFullscreen ? '退出全屏' : '全屏' }}
          <!-- <div v-if="isFullscreen" @click.stop="handleFullscreenClick(3)">退出全屏</div>
          <a-dropdown :trigger="['hover']" v-else>
            <a style="color: #fff">
              {{ isFullscreen ? '退出全屏' : '全屏展示' }}
            </a>
            <a-menu  @click="handleFullscreenClick" slot="overlay" style="background-color: #00065b !important">
              <a-menu-item key="0"> 窗口全屏 </a-menu-item>
              <a-menu-item key="1"> 网页全屏 </a-menu-item>
            </a-menu>
          </a-dropdown> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  name: 'DatavNav',
})
export default class DatavNav extends Vue {
  @Prop({ type: Boolean, required: false }) isFullscreen!: boolean;
  activeIndex = 0;

  handleNavLeftClick(index: number) {
    this.activeIndex = index;
    this.$emit('nav-click', index);
  }

  handleNavRightClick(index: number) {
    this.activeIndex = index;
    this.$emit('nav-click', index);
  }

  handleFullscreenClick(menuItem: any) {
    console.log(menuItem);
    this.$emit('fullscreen-click', menuItem.key);
  }
}
</script>

<style scoped lang="less">
:deep(.ant-dropdown-menu-item) {
  color: #fff;
}
.datav-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;

  &-left {
    flex: 1;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  &-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  &-left-item,
  &-right-item {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 24px;
  }

  &-left-item-title,
  &-right-item-title {
    padding: 0 16px;
    height: 40px;
    background: url(../../assets/images/datav-btn.png) center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
  }

  &-item-title-active {
    background: url(../../assets/images/datav-btn-active.png) no-repeat center;
    padding: 0 16px;
    height: 40px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
  }
}
</style>
