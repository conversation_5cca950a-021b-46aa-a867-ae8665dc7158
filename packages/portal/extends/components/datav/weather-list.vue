<template>
  <div class="weather-cards-container">
    <!-- 右侧卡片组 -->
    <div class="cards-group" @click="handleShowWeekForecast">
      <div
        v-for="(item, index) in weatherList"
        :key="index"
        class="weather-card"
        :class="item.condition"
      >
        <div class="weather-header">
          <div class="weather-date">
            {{ index === 0 ? '今天' : '明天' }} {{item.isDaytime ? '白天' : '夜间'}}的天气
            <!-- <span
              class="day-night-icon"
              :class="item.isDaytime ? 'day-icon' : 'night-icon'"
            ></span> -->
          </div>
          <div class="weather-quality" :class="getQualityClass(item.quality)">
            {{ item.quality }}
          </div>
        </div>
        <div class="weather-content">
          <div class="weather-main">
            <div class="weather-icon"></div>
            <div class="weather-info">
              <div class="weather-type">{{ item.conditionText }}</div>
              <div class="weather-temp">
                <span>{{ item.temperature }}°C</span>
              </div>
            </div>
          </div>
          <div class="weather-stats">
            <div class="weather-stat-item">
              <div class="stat-icon humidity-icon"></div>
              <div class="stat-value">{{ item.humidity }}%</div>
            </div>
            <div class="weather-stat-item">
              <div class="stat-icon wind-icon"></div>
              <div class="stat-value">{{ item.wind }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import moment from 'moment';
@Component({
  name: 'DatavWeatherList',
}) 
export default class DatavWeatherList extends Vue {
  @Prop({ type: Object, default: () => {} }) weatherData!: any;

  weatherList: any[] = [];

  getQualityClass(quality: string): string {
    if (quality.indexOf('优') !== -1) {
      return 'excellent';
    } else if (quality.indexOf('良') !== -1) {
      return 'good';
    } else if (quality.indexOf('轻度') !== -1) {
      return 'moderate';
    } else if (quality.indexOf('中度') !== -1) {
      return 'unhealthy';
    } else if (quality.indexOf('重度') !== -1) {
      return 'very-unhealthy';
    } else if (quality.indexOf('严重') !== -1) {
      return 'hazardous';
    }
  }

  getWeatherIcon(weather: string) {
    if (weather.indexOf('晴') !== -1) {
      return 'sunny';
    } else if (weather.indexOf('多云') !== -1) {
      return 'cloudy';
    } else if (weather.indexOf('阴') !== -1) {
      return 'cloudy';
    } else if (weather.indexOf('雨') !== -1) {
      return 'rainy';
    } else if (weather.indexOf('雪') !== -1) {
      return 'snowy';
    } else if (weather.indexOf('雾') !== -1) {
      return 'foggy';
    }
  }

  handleShowWeekForecast() {
    this.$emit('click', this.weatherData);
  }

  @Watch('weatherData', { immediate: true })
  onWeatherListChange(newVal: any) {
    // 检查newVal和forecastDays是否存在
    if (!newVal || !newVal.forecastDays) {
      this.weatherList = [];
      return;
    }
    const today = moment().format('YYYY-MM-DD');
    const tomorrow = moment().add(1, 'day').format('YYYY-MM-DD');
    const currentForecastDays = newVal.forecastDays.forecastDay;
    this.weatherList = currentForecastDays
      .map((item: any, index) => {
        // 获取当前时间
        const now = moment();

        // 从数据中获取日出日落时间
        const sunRise = item.sunRise
          ? moment.unix(item.sunRise)
          : moment().hour(6).minute(0);
        const sunSet = item.sunDown
          ? moment.unix(item.sunDown)
          : moment().hour(18).minute(0);

        // 获取预报日期
        const forecastDate = item.predictDate
          ? moment.unix(item.predictDate).format('YYYY-MM-DD')
          : '';

        // 判断是否为今天
        const isToday = forecastDate === today;

        // 今天根据当前时间判断，明天默认显示白天
        const isDaytime = isToday
          ? now.isAfter(sunRise) && now.isBefore(sunSet)
          : true; // 明天默认显示白天

        // 根据白天/夜间选择对应的天气信息
        const weatherType = isDaytime ? item.weatherDay : item.weatherNight;
        const weatherIcon = this.getWeatherIcon(weatherType);
        const windDir = isDaytime ? item.windDirDay : item.windDirNight;
        const windLevel = isDaytime ? item.windLevelDay : item.windLevelNight;

        return {
          condition: weatherIcon,
          conditionText: weatherType,
          temperature: isDaytime ? item.tempHigh : item.tempLow,
          humidity: item.humidity,
          wind: `${windDir} ${windLevel}级`,
          quality: item.aqiDesc,
          isDaytime: isDaytime,
          icon: isDaytime ? item.iconDay : item.iconNight,
          forecastDate: forecastDate,
        };
      })
      .filter(
        (item) => item.forecastDate === today || item.forecastDate === tomorrow,
      )
      .slice(0, 2); // 仍然保留slice以确保最多只返回两条数据
    console.log('🚀 ~ DatavWeatherList ~ this.weatherList:', this.weatherList);
  }
}
</script>
<style lang="less" scoped>
.weather-cards-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 100%;

  .cards-group {
    display: flex;
    width: 100%;
    gap: 2%;
    justify-content: space-between;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 1px;
      height: 80%;
      background: rgba(1, 202, 217, 0.3);
      transform: translate(-50%, -50%);
      border-radius: 1px;
      box-shadow: 0 0 8px rgba(1, 202, 217, 0.2);
    }
  }
}

.weather-card {
  width: calc(50% - 1%);
  height: 100%;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 2.5%;
  color: white;
  box-sizing: border-box;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2),
    0 0 10px rgba(1, 202, 217, 0.1) inset;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(1, 202, 217, 0.2);
  cursor: pointer;

  .weather-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
    margin-bottom: 4%;

    .weather-date {
      background-color: rgba(1, 202, 217, 0.2);
      padding: 3px 10px;
      border-radius: 10px;
      font-size: 12px;
      font-weight: bold;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 5px;

      .day-night-icon {
        width: 16px;
        height: 16px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        display: inline-block;
      }

      .day-icon {
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><circle cx="12" cy="12" r="5"/><path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/></svg>');
        background-size: 150% 150%;
      }

      .night-icon {
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="black"><path d="M12 3a9 9 0 1 1-9 9 9.78 9.78 0 0 1 .36-2.67A6.55 6.55 0 0 0 8.53 5.2 9.77 9.77 0 0 1 12 3z"/></svg>');
        background-size: 150% 150%;
      }
    }

    .weather-quality {
      font-size: 12px;
      font-weight: bold;
      padding: 3px 10px;
      border-radius: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      &.excellent {
        background-color: #52c41a;
      }

      &.good {
        background-color: #1890ff;
      }

      &.moderate {
        background-color: #fadb14;
      }

      &.unhealthy {
        background-color: #fa8c16;
      }

      &.very-unhealthy {
        background-color: #f5222d;
      }

      &.hazardous {
        background-color: #722ed1;
      }
    }
  }

  .weather-content {
    display: flex;

    .weather-main {
      display: flex;
      align-items: center;
      margin-right: 4%;
      width: 50%;

      .weather-icon {
        width: 40px;
        height: 40px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        margin-right: 8%;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
      }

      .weather-info {
        transform-origin: left center;
        display: flex;
        flex-direction: column;

        .weather-type {
          font-size: 14px;
          font-weight: bold;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          margin-bottom: 4%;
        }

        .weather-temp {
          display: flex;
          align-items: center;

          span {
            font-size: 20px;
            font-weight: bold;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            letter-spacing: 1px;
          }
        }
      }
    }

    .weather-stats {
      display: flex;
      flex-direction: column;
      gap: 6%;
      justify-content: center;
      width: 50%;

      .weather-stat-item {
        display: flex;
        align-items: center;

        .stat-icon {
          width: 20px;
          height: 20px;
          margin-right: 6px;
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
        }

        .humidity-icon {
          background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"/></svg>');
        }

        .wind-icon {
          background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M9.59 4.59A2 2 0 1 1 11 8H2m10.59 11.41A2 2 0 1 0 14 16H2m15.73-8.27A2.5 2.5 0 1 1 19.5 12H2"/></svg>');
        }

        .stat-value {
          font-size: 14px;
        }
      }
    }
  }
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25),
      0 0 15px rgba(1, 202, 217, 0.15) inset;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 255, 255, 0) 50%
    );
    z-index: 1;
  }

  &.sunny {
    background: linear-gradient(135deg, #ffa500, #ff8c00);
    .weather-icon {
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><circle cx="12" cy="12" r="5"/><path d="M12 2v2M12 20v2M4.93 4.93l1.41 1.41M17.66 17.66l1.41 1.41M2 12h2M20 12h2M4.93 19.07l1.41-1.41M17.66 6.34l1.41-1.41"/></svg>');
    }
  }

  &.rainy {
    background: linear-gradient(135deg, #4682b4, #1e3c72);
    .weather-icon {
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242M9 17l1 3M15 17l1 3M12 17l1 3"/></svg>');
    }
  }

  &.cloudy {
    background: linear-gradient(135deg, #708090, #4b5563);
    .weather-icon {
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19 18H6a4 4 0 1 1 0-8h1a5 5 0 0 1 9.9-1H17a3 3 0 0 1 0 6h2a2 2 0 0 1 0 4z"/></svg>');
    }
  }

  &.windy {
    background: linear-gradient(135deg, #483d8b, #2c2654);
    .weather-icon {
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M9.59 4.59A2 2 0 1 1 11 8H2m10.59 11.41A2 2 0 1 0 14 16H2m15.73-8.27A2.5 2.5 0 1 1 19.5 12H2"/></svg>');
    }
  }

  &.snowy {
    background: linear-gradient(135deg, #d1c4e9, #9575cd);
    .weather-icon {
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M20 17.58A5 5 0 0 0 18.42 19H5.58A5 5 0 0 0 4 17.58V12a8 8 0 0 1 16 0zM6 20h12"/></svg>');
    }
  }

  &.foggy {
    background: linear-gradient(135deg, #87ceeb, #6495ed);
    .weather-icon {
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M3 15h18m-18-4h18m-18 8h18"/></svg>');
    }
  }
}
</style>
