<template>
  <div class="datav-sub-title" :style="customStyle">
    <div class="datav-sub-title-text">{{ title }}</div>
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { Prop } from 'vue-property-decorator';

@Component({
  name: 'DatavSubTitle',
})
export default class DatavSubTitle extends Vue {
  @Prop({ type: String, required: false }) title!: string;
  @Prop({ type: String, required: false }) customStyle!: string;
}
</script>
<style lang="less" scoped>
.datav-sub-title {
  display: flex;
  left: 0;
  box-sizing: border-box;
  height: 14%;
  position: absolute;
  width: 49%;
  display: flex;
  justify-content: center;
  align-items: center;
  &-text {
    font-size: 12px;
    color: #fff;
  }
}
</style>
