<template>
  <div class="datav-modal-container" v-if="visible">
    <div
      class="datav-modal-overlay"
      @click="maskClosable && handleCancel()"
    ></div>
    <div
      class="datav-modal"
      :class="{ 'datav-modal-centered': centered }"
      :style="{ width: width }"
      tabindex="-1"
      @keydown.esc="keyboard && handleCancel()"
    >
      <!-- 顶部装饰条 -->
      <div class="datav-modal-decoration-top">
        <span></span><span></span><span></span><span></span>
      </div>

      <!-- 标题区域 -->
      <div class="datav-modal-header">
        <div class="datav-modal-header-content">
          <slot name="title">
            <div class="datav-modal-title">{{ title }}</div>
          </slot>
        </div>
        <div class="datav-modal-close" @click="handleCancel">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path
              fill="currentColor"
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"
            />
          </svg>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="datav-modal-body" :style="bodyStyle">
        <div class="datav-modal-content">
          <slot></slot>
        </div>
      </div>

      <!-- 底部区域 -->
      <div v-if="showFooter || $slots.footer" class="datav-modal-footer">
        <slot name="footer">
          <a-button
            v-if="showCancel"
            class="datav-modal-btn datav-modal-btn-cancel"
            @click="handleCancel"
          >
            {{ cancelText }}
          </a-button>
          <a-button
            v-if="showOk"
            class="datav-modal-btn datav-modal-btn-ok"
            type="primary"
            :loading="confirmLoading"
            @click="handleOk"
          >
            {{ okText }}
          </a-button>
        </slot>
      </div>

      <!-- 底部装饰条 -->
      <div class="datav-modal-decoration-bottom">
        <span></span><span></span><span></span><span></span>
      </div>

      <!-- 角落装饰 -->
      <div class="datav-modal-corner datav-modal-corner-tl"></div>
      <div class="datav-modal-corner datav-modal-corner-tr"></div>
      <div class="datav-modal-corner datav-modal-corner-bl"></div>
      <div class="datav-modal-corner datav-modal-corner-br"></div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Emit } from 'vue-property-decorator';
import { Button } from '@h3/antd-vue';

@Component({
  name: 'DatavModal',
  components: {
    AButton: Button,
  },
})
export default class DatavModal extends Vue {
  @Prop({ type: Boolean, default: false }) visible!: boolean;
  @Prop({ type: String, default: '弹窗标题' }) title!: string;
  @Prop({ type: [String, Number], default: '800px' }) width!: string | number;
  @Prop({ type: Boolean, default: true }) centered!: boolean;
  @Prop({ type: Boolean, default: true }) destroyOnClose!: boolean;
  @Prop({ type: Boolean, default: false }) maskClosable!: boolean;
  @Prop({ type: Boolean, default: true }) keyboard!: boolean;
  @Prop({ type: Boolean, default: true }) showFooter!: boolean;
  @Prop({ type: Boolean, default: true }) showCancel!: boolean;
  @Prop({ type: Boolean, default: true }) showOk!: boolean;
  @Prop({ type: String, default: '取消' }) cancelText!: string;
  @Prop({ type: String, default: '确定' }) okText!: string;
  @Prop({ type: Boolean, default: false }) confirmLoading!: boolean;
  @Prop({ type: Object, default: () => ({}) }) bodyStyle!: Record<
    string,
    string
  >;

  @Prop({ type: String, default: 'datav-modal-wrap' }) wrapClassName!: string;

  @Emit('cancel')
  handleCancel() {
    return;
  }

  @Emit('ok')
  handleOk() {
    return;
  }

  // 组件销毁时处理清理
  beforeDestroy() {
    if (this.destroyOnClose) {
      // 清理可能的副作用
    }
  }
}
</script>

<style scoped lang="less">
// 动画关键帧
@keyframes borderPulse {
  0% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.4;
  }
}

@keyframes cornerGlow {
  0% {
    box-shadow: 0 0 4px 1px #00c3ff;
  }
  50% {
    box-shadow: 0 0 8px 2px #00eeff;
  }
  100% {
    box-shadow: 0 0 4px 1px #00c3ff;
  }
}

@keyframes scanLine {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.datav-modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.datav-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 10, 30, 0.85);
  backdrop-filter: blur(10px);
  z-index: 1;
}

.datav-modal {
  position: relative;
  max-width: 95%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  z-index: 2;
  background: linear-gradient(
    180deg,
    rgba(1, 25, 51, 0.98) 0%,
    rgba(0, 14, 35, 0.98) 100%
  );
  border: 1px solid rgba(0, 180, 255, 0.5);
  border-radius: 6px;
  box-shadow: 0 0 25px rgba(0, 180, 255, 0.3);
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    box-shadow: inset 0 0 20px rgba(0, 180, 255, 0.15);
    pointer-events: none;
    z-index: 0;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: linear-gradient(
      to bottom,
      rgba(0, 220, 255, 0.05) 0%,
      rgba(0, 0, 0, 0) 15%,
      rgba(0, 0, 0, 0) 85%,
      rgba(0, 220, 255, 0.05) 100%
    );
    pointer-events: none;
    z-index: 0;
  }

  &.datav-modal-centered {
    margin: 0 auto;
  }
}

// 装饰条样式
.datav-modal-decoration-top,
.datav-modal-decoration-bottom {
  height: 4px;
  width: 100%;
  background: rgba(0, 180, 255, 0.1);
  display: flex;
  justify-content: space-between;
  padding: 0 60px;

  span {
    height: 100%;
    width: 40px;
    background: #00c3ff;
    opacity: 0.7;
    animation: borderPulse 2s infinite;

    &:nth-child(2) {
      animation-delay: 0.5s;
    }

    &:nth-child(3) {
      animation-delay: 1s;
    }

    &:nth-child(4) {
      animation-delay: 1.5s;
    }
  }
}

// 四角装饰
.datav-modal-corner {
  position: absolute;
  width: 15px;
  height: 15px;
  border: 1px solid #00eeff;
  z-index: 3;
  animation: cornerGlow 3s infinite;

  &.datav-modal-corner-tl {
    top: -1px;
    left: -1px;
    border-right: none;
    border-bottom: none;
  }

  &.datav-modal-corner-tr {
    top: -1px;
    right: -1px;
    border-left: none;
    border-bottom: none;
  }

  &.datav-modal-corner-bl {
    bottom: -1px;
    left: -1px;
    border-right: none;
    border-top: none;
  }

  &.datav-modal-corner-br {
    bottom: -1px;
    right: -1px;
    border-left: none;
    border-top: none;
  }
}

// 标题区域
.datav-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(
    90deg,
    rgba(0, 35, 70, 0.7) 0%,
    rgba(0, 45, 90, 0.95) 50%,
    rgba(0, 35, 70, 0.7) 100%
  );
  border-bottom: 1px solid rgba(0, 180, 255, 0.3);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    height: 1px;
    width: 100%;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 180, 255, 0.8) 50%,
      transparent 100%
    );
  }
}

.datav-modal-header-content {
  flex: 1;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.datav-modal-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  position: relative;
  padding-left: 15px;
  text-shadow: 0 0 10px rgba(0, 180, 255, 0.5);
  letter-spacing: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 18px;
    border-radius: 2px;
    background: linear-gradient(to bottom, #00eeff, #00c3ff);
    box-shadow: 0 0 8px #00c3ff;
  }
}

.datav-modal-close {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  transition: all 0.2s;
  cursor: pointer;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    transform: rotate(90deg);
  }

  svg {
    filter: drop-shadow(0 0 2px #00c3ff);
  }
}

// 内容区域
.datav-modal-body {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 10%;
    width: 80%;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 180, 255, 0.3) 50%,
      transparent 100%
    );
  }

  // 扫描线效果
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: linear-gradient(
      to bottom,
      transparent,
      rgba(0, 180, 255, 0.1) 50%,
      transparent
    );
    pointer-events: none;
    z-index: 1;
    animation: scanLine 8s linear infinite;
    opacity: 0.5;
  }
}

.datav-modal-content {
  position: relative;
  z-index: 2;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  font-size: 14px;

  :deep(h1),
  :deep(h2),
  :deep(h3),
  :deep(h4),
  :deep(h5),
  :deep(h6) {
    color: #fff;
    position: relative;
    margin-bottom: 16px;
    padding-bottom: 8px;
    text-shadow: 0 0 5px rgba(0, 180, 255, 0.5);

    &::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 40px;
      height: 2px;
      background: linear-gradient(90deg, #00c3ff, transparent);
    }
  }

  :deep(p) {
    margin-bottom: 16px;
  }

  :deep(a) {
    color: #00c3ff;
    text-decoration: none;
    transition: all 0.2s;
    position: relative;

    &:hover {
      color: #00eeff;
      text-shadow: 0 0 5px rgba(0, 180, 255, 0.5);
    }
  }
}

// 底部区域
.datav-modal-footer {
  padding: 16px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: linear-gradient(
    90deg,
    rgba(0, 35, 70, 0.7) 0%,
    rgba(0, 45, 90, 0.95) 50%,
    rgba(0, 35, 70, 0.7) 100%
  );
  border-top: 1px solid rgba(0, 180, 255, 0.3);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 1px;
    width: 100%;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 180, 255, 0.8) 50%,
      transparent 100%
    );
  }
}

// 按钮样式
.datav-modal-btn {
  height: 36px;
  padding: 0 20px;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    z-index: -1;
  }

  &.datav-modal-btn-cancel {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.15);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: #fff;
      border-color: rgba(255, 255, 255, 0.3);
      box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
    }
  }

  &.datav-modal-btn-ok {
    background: linear-gradient(
      135deg,
      rgba(0, 180, 255, 0.9),
      rgba(0, 120, 215, 0.9)
    );
    color: #fff;
    border: 1px solid #00c3ff;
    box-shadow: 0 0 10px rgba(0, 180, 255, 0.3);

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 70%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transform: skewX(-25deg);
      transition: all 0.5s;
    }

    &:hover {
      background: linear-gradient(
        135deg,
        rgba(0, 200, 255, 1),
        rgba(0, 140, 235, 1)
      );
      box-shadow: 0 0 15px rgba(0, 180, 255, 0.5);

      &::after {
        left: 150%;
        transition: all 0.5s;
      }
    }

    &:active {
      transform: translateY(1px);
      box-shadow: 0 0 8px rgba(0, 180, 255, 0.4);
    }
  }
}
</style>
