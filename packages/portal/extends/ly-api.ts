import Axios from 'axios';
import env from '../src/config/env';
import qs from 'qs';

const currentLocation = '101050901';

const govApi = 'https://www.mem.gov.cn';

const alarmDetailApi = `https://www.weather.com.cn/alarm/newalarmcontent.shtml?file=`;

const listApi = `${env.oauthHost}/api/runtime/query/list`;

const getFormUrlApi = `${env.oauthHost}/api/runtime/form/get_form_url`;

const loadFormApi = `${env.oauthHost}/api/runtime/form/load`;

const downloadFileApi = `${env.oauthHost}/api/aliyun/download`;

const serviceTestApi = `${env.oauthHost}/api/bizservice/method/service_test`;

const exportApi = `${env.oauthHost}/api/runtime/query/export_data/async`;

const exportFile = `${env.oauthHost}/api/runtime/query/export_data/async/export_file`;

const govWarningInfoApi = `${env.oauthHost.split('/')[0]}/fw/`;

const currentWeatherApi = `${
  env.oauthHost.split('/')[0]
}/weather/weather1d/${currentLocation}.shtml`;

const alarmApi = `${env.oauthHost.split('/')[0]}/alarm/grepalarm_cn.php?_=${Date.now()}`;

const currentLocationSk_2dApi = `${
  env.oauthHost.split('/')[0]
}/d1/sk_2d/${currentLocation}.html?_=${Date.now()}`;

const currentMojiWeatherApi = `${env.oauthHost.split('/')[0]}/moji/fc40`;

interface ListParams {
  filters: Array<any>;
  mobile: Boolean;
  page: Number;
  queryCode: String;
  schemaCode: String;
  size: Number;
  queryVersion: Number;
  queryCondition: Array<any>;
  showTotal: Boolean;
}

export default {
  // 获取列表信息
  getList(params: ListParams): Promise<any> {
    return Axios.post(`${listApi}`, params);
  },

  getFormUrl(params: any): Promise<any> {
    return Axios.get(`${getFormUrlApi}`, { params });
  },

  loadForm(params: any): Promise<any> {
    return Axios.get(`${loadFormApi}`, { params });
  },

  downloadFile(params: any): Promise<any> {
    return (window as any).open(`${downloadFileApi}?refId=${params.refId}`);
  },

  getServiceTestApi(params: any): Promise<any> {
    return Axios.post(`${serviceTestApi}`, params);
  },

  exportData(params: any): Promise<any> {
    return Axios.post(`${exportApi}`, params);
  },

  exportFile(params: any): Promise<any> {
    return Axios.post(`${exportFile}`, params);
  },

  // 获取政府警示信息
  getGovWarningInfo(params: any): Promise<any> {
    return Axios.get(`${govWarningInfoApi}`, { params });
  },

  getGovApi() {
    return govApi + '/fw/';
  },

  getAlarmApi() {
    return alarmDetailApi;
  },

  // 获取天气信息
  getCurrentWeatherInfo(params: any): Promise<any> {
    return Axios.get(`${currentWeatherApi}`, { params });
  },

  getCurrentLocationSk_2dInfo(params: any): Promise<any> {
    return Axios.get(`${currentLocationSk_2dApi}`, {
      params,
    });
  },

  // 获取墨迹天气信息
  getCurrentMojiWeatherInfo(params: any): Promise<any> {
    return Axios.post(`${currentMojiWeatherApi}`, params);
  },

  getAlarmInfo(params: any): Promise<any> {
    return Axios.get(`${alarmApi}`, { 
      headers: {
      'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
      'Cookie': 'f_city=北京|101010100|,上海|101020100|,广州|101280101|',
      }, params });
  },
};
