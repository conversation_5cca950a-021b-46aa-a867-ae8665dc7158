<template>
  <div class="home-item-box my-custiom">
    <div>
      <h3-swiper loop auto  style="height: 80px;">
        <h3-swiper-item
          v-for="(item, index) in newImgList?.designer?.Carousel6.props.photos"
          :key="index"
          class="h3-swiper-demo-img"
        >
          <img :src="`http://**************:8090/api/public/image/person/${item.refId}`"  style="width: 100%; height: 45%;"/>
        </h3-swiper-item>
      </h3-swiper>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { H3Button } from '@h3/thinking-ui';
import { H3Swiper, H3SwiperItem } from 'h3-mobile-vue';
import exportGetCode  from '../../ly-api'
@Component({
  components: {
    H3Button,
    H3Swiper<PERSON><PERSON>,
    H3Swiper,
  },
})
export default class CustomNews extends Vue {

  newImgList:any = [];
  created() {
    this.fetchNewsList()
  }
  async fetchNewsList() {
    const data = await exportGetCode.exportGetCode({
      params:{
      code:'defaultPortalDashboard'
      }
    });
    this.newImgList = JSON.parse(data.data.draftPortalJson)

  }
}
</script>

<style lang="less" scoped>
@import '~@/styles/mixins.less';

</style>
